using System;
using System.Collections.Generic;
using System.Linq;
using Domain.Entities;

namespace Database.Repositories.Simulation.Extensions;

public static class SimulationSectionCollectionExtensions
{
    public static object ToDbWriteParam(
        this List<SimulationSection> simulationSections,
        Guid simulationId)
    {
        return simulationSections.Select(simulationSection => new
        {
            simulationSection.Id,
            SectionReviewId = simulationSection.SectionReview.Id,
            SimulationId = simulationId,
            SectionId = simulationSection.Section.Id,
            ConstructionStageId = simulationSection.ConstructionStage?.Id,
            simulationSection.MinimumDrainedDepth,
            simulationSection.MinimumUndrainedDepth,
            simulationSection.MinimumPseudoStaticDepth,
            simulationSection.BeachLengthStatisticalMeasure,
            simulationSection.BeachLength,
        });
    }
}
