using System;
using System.Collections.Generic;
using System.Linq;
using Domain.Enums;

namespace Database.Repositories.Simulation.Extensions;

public static class CalculationMethodCollectionExtensions
{
    public static object ToDbWriteParam(
        this List<CalculationMethod> calculationMethods,
        Guid simulationId)
    {
        return calculationMethods.Select(method => new
        {
            SimulationId = simulationId,
            CalculationMethod = method
        });
    }
}
