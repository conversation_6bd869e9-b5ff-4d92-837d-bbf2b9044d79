using System;
using System.Collections.Generic;
using System.Linq;
using Domain.Entities;

namespace Database.Repositories.Simulation.Extensions;

public static class SimulationEventCollectionExtensions
{
    public static object ToDbWriteParam(
        this List<SimulationEvent> events,
        Guid simulationId)
    {
        return events.Select(@event => new
        {
            @event.Id, @event.Event, SimulationId = simulationId,
        });
    }
}
