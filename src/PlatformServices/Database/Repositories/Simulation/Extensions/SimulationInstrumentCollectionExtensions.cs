using System;
using System.Collections.Generic;
using System.Linq;
using Domain.Entities;

namespace Database.Repositories.Simulation.Extensions;

public static class SimulationInstrumentCollectionExtensions
{
    public static object ToDbWriteParam(
        this List<SimulationInstrument> instruments,
        Guid sectionId)
    {
        return instruments
            .Select(instrument => new
            {
                instrument.Id,
                SimulationSectionId = sectionId,
                InstrumentId = instrument.Instrument.Id,
                MeasurementId = instrument.Measurement?.Id,
                instrument.Quota,
                instrument.DryType,
                instrument.Dry
            });
    }
}
