using Domain.Core;
using Domain.Enums;
using Domain.ValueObjects;
using System;
using System.Collections.Generic;

namespace Domain.Entities
{
    public class Simulation : Entity
    {
        public string Name { get; set; }
        public bool ShouldKeep { get; set; }
        public SimulationStatus Status { get; set; }    
        public List<SimulationSection> Sections { get; set; } = new();
        public Slide2Configuration Slide2Configuration { get; set; } = new();
        public bool ShouldEvaluateDrainedCondition { get; set; }
        public bool ShouldEvaluateUndrainedCondition { get; set; }
        public bool ShouldEvaluatePseudoStaticCondition { get; set; }
        public double? SafetyFactorTarget { get; set; }
        public Orientation SeismicCoefficient { get; set; }
        public WaterTableConfiguration WaterTableConfiguration { get; set; }
        public ReadingStatisticalMeasure? ReadingStatisticalMeasure { get; set; }
        public double? WaterTableVariation { get; set; }
        public DateTime? StartDate { get; set; }    
        public DateTime? EndDate { get; set; }
        public Instrument UpstreamLinimetricRuler { get; set; }
        public Instrument DownstreamLinimetricRuler { get; set; }
        public ReadingStatisticalMeasure? UpstreamLinimetricRulerStatisticalMeasure { get; set; }
        public ReadingStatisticalMeasure? DownstreamLinimetricRulerStatisticalMeasure { get; set; }
        public double? UpstreamLinimetricRulerQuota { get; set; }
        public double? DownstreamLinimetricRulerQuota { get; set; }
        public bool IgnoreDamagedInstruments { get; set; }
        public bool NeedToDoStatisticalCalculations { get; set; }
        public User CreatedBy { get; set; }
        public List<User> AuthorizedUsers { get; set; } = new();
        public List<SimulationEvent> Events { get; set; } = new();
    }
}
