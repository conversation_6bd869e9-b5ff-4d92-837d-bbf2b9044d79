using System;
using System.Linq;
using System.Threading.Tasks;
using Application.Core;
using Application.Extensions;
using Application.Services.BlobStorage;
using Database.Repositories.Simulation;
using Model._Shared.File;
using Model.Simulation.GetById.Request;
using Model.Simulation.GetById.Response;
using static Application.Core.UseCaseResponseFactory<
    Model.Simulation.GetById.Response.GetSimulationByIdResponse>;

namespace Application.Simulation.GetById;

public sealed class GetSimulationByIdUseCase : IGetSimulationByIdUseCase
{
    private readonly ISimulationRepository _simulationRepository;
    private readonly IBlobStorageService _blobService;

    private readonly GetSimulationByIdRequestValidator
        _requestValidator = new();

    public GetSimulationByIdUseCase(
        ISimulationRepository simulationRepository,
        IBlobStorageService blobService)
    {
        _simulationRepository = simulationRepository;
        _blobService = blobService;
    }

    public async Task<UseCaseResponse<GetSimulationByIdResponse>> Execute(
        GetSimulationByIdRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(null, "000", "Request cannot be null.");
            }

            var validationResult = await _requestValidator
                .ValidateAsync(request);

            if (!validationResult.IsValid)
            {
                return BadRequest(
                    null,
                    validationResult.Errors.ToErrorMessages());
            }

            var result = await _simulationRepository.GetByIdAsync(request);

            if (result is null)
            {
                return NoContent();
            }
            
            var downloadTasks = result.Sections
                .SelectMany(section => section.Results)
                .SelectMany(simulationResult => new[]
                {
                    simulationResult.DxfFile != null 
                        ? DownloadFileAsync(simulationResult.DxfFile)
                        : Task.CompletedTask,
                    simulationResult.PngFile != null 
                        ? DownloadFileAsync(simulationResult.PngFile)
                        : Task.CompletedTask
                })
                .Where(task => task != Task.CompletedTask);

            await Task.WhenAll(downloadTasks);

            return Ok(result);
        }
        catch (Exception e)
        {
            return InternalServerError(
                null,
                errors: e.ToErrorMessages("000"));
        }
    }
    
    private async Task DownloadFileAsync(File file)
    {
        file.Base64 = Convert.ToBase64String(
            await _blobService.GetAsync(file.UniqueName));
    }
}
