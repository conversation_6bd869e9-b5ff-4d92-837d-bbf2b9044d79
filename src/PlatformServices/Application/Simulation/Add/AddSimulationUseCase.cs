using System;
using System.Linq;
using System.Threading.Tasks;
using Application.Core;
using Application.Extensions;
using Database.Repositories.Instrument;
using Database.Repositories.Reading;
using Database.Repositories.Section;
using Database.Repositories.Simulation;
using Database.Repositories.User;
using Domain.Entities;
using Domain.Enums;
using Domain.ValueObjects;
using Model.Simulation.Add.Request;
using static Application.Core.UseCaseResponseFactory<System.Guid>;

namespace Application.Simulation.Add
{
    public sealed class AddSimulationUseCase : IAddSimulationUseCase
    {
        private readonly IUserRepository _userRepository;
        private readonly ISectionRepository _sectionRepository;
        private readonly IReadingRepository _readingRepository;
        private readonly ISimulationRepository _simulationRepository;
        private readonly IInstrumentRepository _instrumentRepository;
        private readonly AddSimulationRequestValidator _requestValidator = new();

        public AddSimulationUseCase(
            IUserRepository userRepository,
            ISectionRepository sectionRepository,
            ISimulationRepository simulationRepository,
            IReadingRepository readingRepository,
            IInstrumentRepository instrumentRepository)
        {
            _userRepository = userRepository;
            _sectionRepository = sectionRepository;
            _simulationRepository = simulationRepository;
            _readingRepository = readingRepository;
            _instrumentRepository = instrumentRepository;
        }

        public async Task<UseCaseResponse<Guid>> Execute(AddSimulationRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest(Guid.Empty, "000", "Request cannot be null.");
                }

                await request.AddRequesterStructures(_userRepository);

                var validationResult = await _requestValidator
                    .ValidateAsync(request);

                if (!validationResult.IsValid)
                {
                    return BadRequest(Guid.Empty,
                        validationResult.Errors.ToErrorMessages());
                }

                var sectionsDb = await _sectionRepository.GetAsync(request.Sections.Select(x => x.SectionId));

                if (!sectionsDb.Any())
                {
                    return BadRequest(Guid.Empty, "000", "One or more sections do not exist.");
                }

                if (sectionsDb.Count() != request.Sections.Count)
                {
                    return BadRequest(Guid.Empty, "000", "One or more sections do not exist.");
                }

                if (sectionsDb.Select(x => x.Structure.Id).Distinct().Count() > 1)
                {
                    return BadRequest(Guid.Empty, "000", "All sections must be from the same structure.");
                }

                if (!request.RequestedBySuperSupport && !request.RequestedUserStructures.Exists(x => x == sectionsDb.First().Structure.Id))
                {
                    return Forbidden(Guid.Empty, "000", "User does not have access to the structure.");
                }

                foreach (var section in sectionsDb)
                {
                    if (!section.Reviews.Any())
                    {
                        return BadRequest(Guid.Empty, "000", $"Section {section.Name} does not have any reviews.");
                    }

                    var requestSection = request.Sections.First(x => x.SectionId == section.Id);

                    if (!requestSection.SectionReviewId.HasValue)
                    {
                        foreach (var review in section.Reviews.OrderByDescending(x => x.StartDate))
                        {
                            if (AreFilesValid(review.Drawing, review.Sli))
                            {
                                requestSection.SectionReviewId = review.Id;
                                break;
                            }
                            else if (review.ConstructionStages.Any())
                            {
                                var currentStage = review.ConstructionStages
                                    .FirstOrDefault(x => x.IsCurrentStage);

                                if (currentStage != null && AreFilesValid(currentStage.Drawing, currentStage.Sli))
                                {
                                    requestSection.SectionReviewId = review.Id;
                                    requestSection.ConstructionStageId = currentStage.Id;
                                    break;
                                }

                                foreach (var stage in review.ConstructionStages.OrderByDescending(x => x.Stage))
                                {
                                    if (AreFilesValid(stage.Drawing, stage.Sli))
                                    {
                                        requestSection.SectionReviewId = review.Id;
                                        requestSection.ConstructionStageId = stage.Id;
                                        break;
                                    }
                                }
                            }
                        }

                        if (!requestSection.SectionReviewId.HasValue)
                        {
                            return BadRequest(Guid.Empty, "000", $"No valid review found for section {section.Name} with both DXF and SLI files.");
                        }

                        continue;
                    }

                    var selectedReview = section.Reviews.FirstOrDefault(x => x.Id == requestSection.SectionReviewId);

                    if (selectedReview == null)
                    {
                        return BadRequest(Guid.Empty, "000", $"Review {requestSection.SectionReviewId} does not exist in section {section.Name}.");
                    }

                    if (!selectedReview.ConstructionStages.Any() && !AreFilesValid(selectedReview.Drawing, selectedReview.Sli))
                    {
                        return BadRequest(Guid.Empty, "000", $"Review {selectedReview.Id} does not have valid files.");
                    }

                    if (selectedReview.ConstructionStages.Any())
                    {
                        if (!requestSection.ConstructionStageId.HasValue)
                        {
                            var currentStage = selectedReview.ConstructionStages
                                .FirstOrDefault(x => x.IsCurrentStage);

                            if (currentStage != null && AreFilesValid(currentStage.Drawing, currentStage.Sli))
                            {
                                requestSection.ConstructionStageId = currentStage.Id;
                                continue;
                            }

                            foreach (var stage in selectedReview.ConstructionStages.OrderByDescending(x => x.Stage))
                            {
                                if (AreFilesValid(stage.Drawing, stage.Sli))
                                {
                                    requestSection.SectionReviewId = selectedReview.Id;
                                    requestSection.ConstructionStageId = stage.Id;
                                    break;
                                }
                            }

                            if (!requestSection.ConstructionStageId.HasValue)
                            {
                                return BadRequest(Guid.Empty, "000", $"No valid construction stage found for review {selectedReview.Id}.");
                            }
                        }
                        else
                        {
                            var selectedStage = selectedReview.ConstructionStages
                                .FirstOrDefault(x => x.Id == requestSection.ConstructionStageId);

                            if (selectedStage == null)
                            {
                                return BadRequest(Guid.Empty, "000", $"Construction stage {requestSection.ConstructionStageId} does not exist in review {selectedReview.Id}.");
                            }

                            if (!AreFilesValid(selectedStage.Drawing, selectedStage.Sli))
                            {
                                return BadRequest(Guid.Empty, "000", $"Construction stage {selectedStage.Id} does not have valid files.");
                            }
                        }
                    }
                    else if (requestSection.ConstructionStageId.HasValue)
                    {
                        return BadRequest(Guid.Empty, "000", $"Construction stage {requestSection.ConstructionStageId} does not exist in review {selectedReview.Id}.");
                    }
                }

                var linimetricRulers = await _instrumentRepository
                    .GetByTypeAndStructureAsync(InstrumentType.LinimetricRuler, sectionsDb.First().Structure.Id);

                foreach (var section in request.Sections)
                {
                    var sectionDb = sectionsDb.First(x => x.Id == section.SectionId);

                    if (section.Instruments.Any())
                    {
                        foreach (var instrument in section.Instruments)
                        {
                            var instrumentDb = sectionDb.Instruments.Find(x => x.Id == instrument.InstrumentId);

                            if (instrumentDb == null)
                            {
                                return BadRequest(Guid.Empty, "000", $"Instrument {instrument.InstrumentId} does not exist in section {sectionDb.Name}.");
                            }

                            if (instrumentDb.Type != InstrumentType.WaterLevelIndicator && instrumentDb.Type != InstrumentType.ElectricPiezometer && instrumentDb.Type != InstrumentType.OpenStandpipePiezometer)
                            {
                                return BadRequest(Guid.Empty, "000", $"Instrument {instrumentDb.Identifier} is not valid. Only water level indicators or piezometers are allowed to select.");
                            }

                            instrument.DryType = (DryType)instrumentDb.DryType;
                            instrument.BaseQuota = instrumentDb.BaseQuota ?? 0;

                            if (instrumentDb.Type == InstrumentType.ElectricPiezometer)
                            {
                                var measurementDb = instrumentDb.Measurements.Find(x => x.Id == instrument.MeasurementId);

                                if (measurementDb == null)
                                {
                                    return BadRequest(Guid.Empty, "000", $"The measurement was not found in the instrument {instrumentDb.Identifier}.");
                                }

                                if (!measurementDb.Active)
                                {
                                    return BadRequest(Guid.Empty, "000", $"Measurement {measurementDb.Identifier} is not active.");
                                }

                                instrument.BaseQuota = measurementDb.Quota;
                            }

                            if (instrument.Quota.HasValue)
                            {
                                if (instrument.Quota > instrumentDb.TopQuota || instrument.Quota < instrument.BaseQuota)
                                {
                                    return BadRequest(Guid.Empty, "000", $"Quota of instrument {instrumentDb.Identifier} must be between {instrument.BaseQuota} and {instrumentDb.TopQuota}.");
                                }
                            }

                            if (request.IgnoreDamagedInstruments && !instrumentDb.Online)
                            {
                                return BadRequest(Guid.Empty, "000", $"Instrument {instrumentDb.Identifier} is offline.");
                            }
                        }
                    }
                    else if(request.WaterTableConfiguration != WaterTableConfiguration.Dxf)
                    {
                        request.NeedToDoStatisticalCalculations = true;

                        foreach (var instrument in sectionDb.Instruments)
                        {
                            if (request.IgnoreDamagedInstruments && !instrument.Online)
                            {
                                continue;
                            }

                            if (instrument.Type == InstrumentType.ElectricPiezometer)
                            {
                                foreach (var measurement in instrument.Measurements.Where(x => x.Active))
                                {
                                    section.Instruments.Add(new()
                                    {
                                        BaseQuota = measurement.Quota,
                                        Dry = false,
                                        DryType = instrument.DryType ?? DryType.Base,
                                        InstrumentId = instrument.Id,
                                        MeasurementId = measurement.Id,
                                        Quota = 0
                                    });
                                }
                            }
                            else if (instrument.Type == InstrumentType.OpenStandpipePiezometer || instrument.Type == InstrumentType.WaterLevelIndicator)
                            {
                                section.Instruments.Add(new()
                                {
                                    BaseQuota = instrument.BaseQuota ?? 0,
                                    Dry = false,
                                    DryType = instrument.DryType ?? DryType.Base,
                                    InstrumentId = instrument.Id,
                                    Quota = 0
                                });
                            }
                        }
                    }

                    var reviewDb = sectionDb.Reviews.FirstOrDefault(x => x.Id == section.SectionReviewId);

                    if (reviewDb == null)
                    {
                        return BadRequest(Guid.Empty, "000", $"Review {section.SectionReviewId} does not exist in section {sectionDb.Name}.");
                    }

                    if (!sectionDb.Instruments.Any())
                    {
                        if (section.ConstructionStageId.HasValue)
                        {
                            var constructionStageDb = reviewDb.ConstructionStages.FirstOrDefault(x => x.Id == section.ConstructionStageId);

                            if (!constructionStageDb.DxfHasWaterline)
                            {
                                return BadRequest(Guid.Empty, "000", $"The section has no instruments and no waterline was found within the DXF of the section {sectionDb.Name} construction stage {constructionStageDb.Stage}.");
                            }
                        }
                        else if(!reviewDb.DxfHasWaterline)
                        {
                            return BadRequest(Guid.Empty, "000", $"The section has no instruments and no waterline was found within the DXF of the section {sectionDb.Name} review {reviewDb.Index}.");
                        }
                    }

                    if (section.BeachLengthStatisticalMeasure != null
                        && section.BeachLengthStatisticalMeasure != ReadingStatisticalMeasure.Specific
                        && section.BeachLengthStatisticalMeasure != ReadingStatisticalMeasure.Latest)
                    {
                        request.NeedToDoStatisticalCalculations = true;

                        var existsReadings = await _readingRepository.ExistsBeachLengthAsync(section.SectionId);

                        if (!existsReadings)
                        {
                            return BadRequest(Guid.Empty, "000", $"Calculations options for beach length were selected, but there are no readings for the beach length in the section {sectionDb.Name}.");
                        }
                    }
                }

                if (request.DownstreamLinimetricRulerStatisticalMeasure != null)
                {
                    if (linimetricRulers == null || !linimetricRulers.Any())
                    {
                        return BadRequest(Guid.Empty, "000", $"Calculations options for downstream linimetric ruler were selected, but there are no linimetric ruler in the structure {sectionsDb.First().Structure.Name}.");
                    }

                    if (!linimetricRulers.Exists(x => x.LinimetricRulerPosition == LinimetricRulerPosition.Downstream && x.Online))
                    {
                        return BadRequest(Guid.Empty, "000", $"Calculations options for downstream linimetric ruler were selected, but there are no downstream linimetric ruler in the structure {sectionsDb.First().Structure.Name}.");
                    }

                    var linimetricRulerInstrument = linimetricRulers.First(x => x.LinimetricRulerPosition == LinimetricRulerPosition.Downstream && x.Online);

                    request.DownstreamLinimetricRulerId = linimetricRulerInstrument.Id;

                    if (request.DownstreamLinimetricRulerStatisticalMeasure != ReadingStatisticalMeasure.Specific
                        && request.DownstreamLinimetricRulerStatisticalMeasure != ReadingStatisticalMeasure.Latest)
                    {
                        request.NeedToDoStatisticalCalculations = true;

                        var existsReadings = await _readingRepository.ExistsAsync(linimetricRulerInstrument.Id);

                        if (!existsReadings)
                        {
                            return BadRequest(Guid.Empty, "000", $"There are no readings for the downstream linimetric ruler in the sections of the structure {sectionsDb.First().Structure.Name}.");
                        }
                    }
                }

                if (request.UpstreamLinimetricRulerStatisticalMeasure != null)
                {
                    if (linimetricRulers == null || !linimetricRulers.Any())
                    {
                        return BadRequest(Guid.Empty, "000", $"Calculations options for upstream linimetric ruler were selected, but there are no linimetric ruler in the structure {sectionsDb.First().Structure.Name}.");
                    }

                    if (!linimetricRulers.Exists(x => x.LinimetricRulerPosition == LinimetricRulerPosition.Upstream && x.Online))
                    {
                        return BadRequest(Guid.Empty, "000", $"Calculations options for upstream linimetric ruler were selected, but there are no upstream linimetric ruler in the sections of the structure {sectionsDb.First().Structure.Name}.");
                    }

                    var linimetricRulerInstrument = linimetricRulers.First(x => x.LinimetricRulerPosition == LinimetricRulerPosition.Upstream && x.Online);
                    request.UpstreamLinimetricRulerId = linimetricRulerInstrument.Id;

                    if (request.UpstreamLinimetricRulerStatisticalMeasure != ReadingStatisticalMeasure.Specific
                        && request.UpstreamLinimetricRulerStatisticalMeasure != ReadingStatisticalMeasure.Latest)
                    {
                        request.NeedToDoStatisticalCalculations = true;

                        var existsReadings = await _readingRepository.ExistsAsync(linimetricRulerInstrument.Id);

                        if (!existsReadings)
                        {
                            return BadRequest(Guid.Empty, "000", $"There are no readings for the upstream linimetric ruler in the sections of the structure {sectionsDb.First().Structure.Name}.");
                        }
                    }
                }

                var simulation = Convert(request);

                await _simulationRepository.AddAsync(simulation);

                return Ok(simulation.Id);
            }
            catch (Exception e)
            {
                return InternalServerError(Guid.Empty, errors: e.ToErrorMessages("000"));
            }
        }

        private static bool AreFilesValid(File dxf, File sli)
        {
            return dxf != null
                    && !string.IsNullOrEmpty(dxf.UniqueName)
                    && sli != null
                    && !string.IsNullOrEmpty(sli.UniqueName);
        }

        private static Domain.Entities.Simulation Convert(AddSimulationRequest request)
        {
            var slide2Configuration = new Slide2Configuration()
            {
                CircularParameters = request.Slide2Configuration.CircularParameters != null
                ? new()
                {
                    CalculationMethods = request.Slide2Configuration.CircularParameters.CalculationMethods,
                    CircularSearchMethod = request.Slide2Configuration.CircularParameters.CircularSearchMethod,
                    DivisionsAlongSlope = request.Slide2Configuration.CircularParameters.DivisionsAlongSlope,
                    CirclesPerDivision = request.Slide2Configuration.CircularParameters.CirclesPerDivision,
                    NumberOfIterations = request.Slide2Configuration.CircularParameters.NumberOfIterations,
                    DivisionsNextIteration = request.Slide2Configuration.CircularParameters.DivisionsNextIteration,
                    RadiusIncrement = request.Slide2Configuration.CircularParameters.RadiusIncrement,
                    NumberOfSurfaces = request.Slide2Configuration.CircularParameters.NumberOfSurfaces
                }
                : null,
                NonCircularParameters = request.Slide2Configuration.NonCircularParameters != null
                ? new()
                {
                    SurfacesPerDivision = request.Slide2Configuration.NonCircularParameters.SurfacesPerDivision,
                    NumberOfNests = request.Slide2Configuration.NonCircularParameters.NumberOfNests,
                    CalculationMethods = request.Slide2Configuration.NonCircularParameters.CalculationMethods,
                    NonCircularSearchMethod = request.Slide2Configuration.NonCircularParameters.NonCircularSearchMethod,
                    DivisionsAlongSlope = request.Slide2Configuration.NonCircularParameters.DivisionsAlongSlope,
                    DivisionsNextIteration = request.Slide2Configuration.NonCircularParameters.DivisionsNextIteration,
                    NumberOfIterations = request.Slide2Configuration.NonCircularParameters.NumberOfIterations,
                    NumberOfVerticesAlongSurface = request.Slide2Configuration.NonCircularParameters.NumberOfVerticesAlongSurface,
                    NumberOfSurfaces = request.Slide2Configuration.NonCircularParameters.NumberOfSurfaces,
                    InitialNumberOfIterations = request.Slide2Configuration.NonCircularParameters.InitialNumberOfIterations,
                    InitialNumberOfSurfaceVertices = request.Slide2Configuration.NonCircularParameters.InitialNumberOfSurfaceVertices,
                    MaximumIterations = request.Slide2Configuration.NonCircularParameters.MaximumIterations,
                    MaximumNumberOfSteps = request.Slide2Configuration.NonCircularParameters.MaximumNumberOfSteps,
                    NumberOfFactorsSafetyComparedBeforeStopping = request.Slide2Configuration.NonCircularParameters.NumberOfFactorsSafetyComparedBeforeStopping,
                    ToleranceForStoppingCriterion = request.Slide2Configuration.NonCircularParameters.ToleranceForStoppingCriterion,
                    NumberOfParticles = request.Slide2Configuration.NonCircularParameters.NumberOfParticles
                }
                : null
            };

            var simulation = new Domain.Entities.Simulation()
            {
                Status = SimulationStatus.Processing,
                Events = new()
                {
                    new() { Event = "Simulação em execução." }
                },
                Name = string.IsNullOrEmpty(request.Name.Trim()) ? DateTime.UtcNow.ToString("dd/MM/yyyy HH:mm") : request.Name,
                DownstreamLinimetricRuler = request.DownstreamLinimetricRulerId != null ? new() { Id = (Guid)request.DownstreamLinimetricRulerId } : null,
                UpstreamLinimetricRuler = request.UpstreamLinimetricRulerId != null ? new() { Id = (Guid)request.UpstreamLinimetricRulerId } : null,
                DownstreamLinimetricRulerQuota = request.DownstreamLinimetricRulerQuota,
                NeedToDoStatisticalCalculations = request.NeedToDoStatisticalCalculations,
                DownstreamLinimetricRulerStatisticalMeasure = request.DownstreamLinimetricRulerStatisticalMeasure,
                EndDate = request.EndDate,
                IgnoreDamagedInstruments = request.IgnoreDamagedInstruments,
                ReadingStatisticalMeasure = request.ReadingStatisticalMeasure,
                SafetyFactorTarget = request.SafetyFactorTarget,
                SeismicCoefficient = request.SeismicCoefficient != null ?
                 new Orientation()
                 {
                     Horizontal = request.SeismicCoefficient.Horizontal,
                     Vertical = request.SeismicCoefficient.Vertical
                 } : null,
                ShouldEvaluateDrainedCondition = request.ShouldEvaluateDrainedCondition,
                ShouldEvaluatePseudoStaticCondition = request.ShouldEvaluatePseudoStaticCondition,
                ShouldEvaluateUndrainedCondition = request.ShouldEvaluateUndrainedCondition,
                Slide2Configuration = slide2Configuration,
                StartDate = request.StartDate,
                UpstreamLinimetricRulerQuota = request.UpstreamLinimetricRulerQuota,
                UpstreamLinimetricRulerStatisticalMeasure = request.UpstreamLinimetricRulerStatisticalMeasure,
                WaterTableConfiguration = request.WaterTableConfiguration,
                WaterTableVariation = request.WaterTableVariation,
                Sections = request.Sections.Select(x => new SimulationSection()
                {
                    Section = new() { Id = x.SectionId },
                    SectionReview = new() { Id = (Guid)x.SectionReviewId },
                    ConstructionStage = x.ConstructionStageId != null ? new() { Id = (Guid)x.ConstructionStageId } : null,
                    BeachLength = x.BeachLength,
                    BeachLengthStatisticalMeasure = x.BeachLengthStatisticalMeasure,
                    MinimumDrainedDepth = x.MinimumDrainedDepth,
                    MinimumPseudoStaticDepth = x.MinimumPseudoStaticDepth,
                    MinimumUndrainedDepth = x.MinimumUndrainedDepth,
                    Instruments = x.Instruments?.Select(i => new SimulationInstrument()
                    {
                        Dry = i.Dry,
                        Quota = i.Quota ?? i.BaseQuota,
                        DryType = i.DryType,
                        Instrument = new() { Id = i.InstrumentId },
                        Measurement = i.MeasurementId != null ? new() { Id = (Guid)i.MeasurementId } : null,
                    }).ToList(),
                }).ToList()
            };

            simulation.AuthorizedUsers.Add(new() { Id = request.RequestedBy });
            simulation.CreatedBy = new() { Id = request.RequestedBy };

            return simulation;
        }
    }
}
