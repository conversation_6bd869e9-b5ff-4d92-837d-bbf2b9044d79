using Domain.Enums;
using System.Text.Json.Serialization;
using System;
using Model._Shared.Slide2Configuration;
using System.Collections.Generic;

namespace Model.Simulation.GetById.Response
{
    public sealed record GetSimulationByIdResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; init; }

        [JsonPropertyName("name")]
        public string Name { get; init; }

        [JsonPropertyName("should_keep")]
        public bool ShouldKeep { get; init; }

        [JsonPropertyName("status")]
        public SimulationStatus Status { get; init; }

        [JsonPropertyName("should_evaluate_drained_condition")]
        public bool ShouldEvaluateDrainedCondition { get; init; }

        [JsonPropertyName("should_evaluate_undrained_condition")]
        public bool ShouldEvaluateUndrainedCondition { get; init; }

        [JsonPropertyName("should_evaluate_pseudo_static_condition")]
        public bool ShouldEvaluatePseudoStaticCondition { get; init; }

        [JsonPropertyName("safety_factor_target")]
        public double? SafetyFactorTarget { get; init; }

        [JsonPropertyName("water_table_configuration")]
        public WaterTableConfiguration WaterTableConfiguration { get; init; }

        [JsonPropertyName("reading_statistical_measure")]
        public ReadingStatisticalMeasure? ReadingStatisticalMeasure
        {
            get;
            init;
        }

        [JsonPropertyName("water_table_variation")]
        public double? WaterTableVariation { get; init; }

        [JsonPropertyName("start_date")]
        public DateTime? StartDate { get; init; }

        [JsonPropertyName("end_date")]
        public DateTime? EndDate { get; init; }

        [JsonPropertyName("upstream_linimetric_ruler_statistical_measure")]
        public ReadingStatisticalMeasure?
            UpstreamLinimetricRulerStatisticalMeasure { get; init; }

        [JsonPropertyName("downstream_linimetric_ruler_statistical_measure")]
        public ReadingStatisticalMeasure?
            DownstreamLinimetricRulerStatisticalMeasure { get; init; }

        [JsonPropertyName("upstream_linimetric_ruler_quota")]
        public double? UpstreamLinimetricRulerQuota { get; init; }

        [JsonPropertyName("downstream_linimetric_ruler_quota")]
        public double? DownstreamLinimetricRulerQuota { get; init; }

        [JsonPropertyName("ignore_damaged_instruments")]
        public bool IgnoreDamagedInstruments { get; init; }

        [JsonPropertyName("upstream_linimetric_ruler_id")]
        public Guid? UpstreamLinimetricRulerId { get; set; }

        [JsonPropertyName("downstream_linimetric_ruler_id")]
        public Guid? DownstreamLinimetricRulerId { get; set; }

        [JsonPropertyName("need_to_do_statistical_calculations")]
        public bool NeedToDoStatisticalCalculations { get; init; }

        [JsonPropertyName("seismic_coefficient")]
        public _Shared.Orientation.Orientation SeismicCoefficient { get; set; }

        [JsonPropertyName("slide2_configuration")]
        public Slide2Configuration Slide2Configuration { get; set; }

        [JsonPropertyName("created_by")]
        public _Shared.User.User CreatedBy { get; set; }

        [JsonPropertyName("sections")]
        public List<GetSimulationByIdSection> Sections { get; init; } = new();

        [JsonPropertyName("events")]
        public List<GetSimulationByIdEvent> Events { get; init; } = new();

        [JsonPropertyName("zip_file_download_url")]
        public string ZipFileDownloadUrl =>
            Status == SimulationStatus.Completed
                ? string.Format(Constants.EndpointUrl.SimulationsZipFile, Id)
                : string.Empty;
    }
}
