using Domain.Enums;
using Model._Shared.File;
using System;
using System.Text.Json.Serialization;

namespace Model.Simulation.GetById.Response
{
    public sealed record GetSimulationByIdResult
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("calculation_method")]
        public CalculationMethod CalculationMethod { get; init; }

        [JsonPropertyName("sli_file_type")]
        public SliFileType SliFileType { get; init; }

        [JsonPropertyName("value")]
        public double Value { get; init; }

        [JsonPropertyName("dxf_file")]
        public File DxfFile { get; set; }

        [JsonPropertyName("png_file")]
        public File PngFile { get; set; }

        [JsonPropertyName("zip_file_download_url")]
        public string ZipFileDownloadUrl =>
            DxfFile is not null || PngFile is not null
                ? string.Format(
                    Constants.EndpointUrl.SimulationsResultZipFile,
                    SimulationId,
                    Id)
                : string.Empty;

        [JsonIgnore]
        public Guid SimulationId { get; set; }

        [JsonIgnore]
        public Guid SimulationSectionId { get; set; }
    }
}
