using Application.Core;
using Application.Simulation.Add;
using Application.Tests.Instrument.Builders;
using Application.Tests.Section.Builders;
using Application.Tests.Simulation.Builders;
using Database.Repositories.Instrument;
using Database.Repositories.Outbox;
using Database.Repositories.Reading;
using Database.Repositories.Section;
using Database.Repositories.Simulation;
using Database.Repositories.User;
using Domain.Enums;
using FluentAssertions;
using Model.Simulation.Add.Request;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Sdk;

namespace Application.Tests.Simulation
{
    [Trait("AddSimulationsUseCase", "Execute")]
    public class AddTests
    {
        private readonly Mock<IUserRepository> _userRepository;
        private readonly Mock<ISectionRepository> _sectionRepository;
        private readonly Mock<IReadingRepository> _readingRepository;
        private readonly Mock<ISimulationRepository> _simulationRepository;
        private readonly Mock<IInstrumentRepository> _instrumentRepository;
        private readonly IAddSimulationUseCase _useCase;

        public AddTests()
        {
            _userRepository = new Mock<IUserRepository>();
            _sectionRepository = new Mock<ISectionRepository>();
            _readingRepository = new Mock<IReadingRepository>();
            _simulationRepository = new Mock<ISimulationRepository>();
            _instrumentRepository = new Mock<IInstrumentRepository>();

            _useCase = new AddSimulationUseCase(
                _userRepository.Object,
                _sectionRepository.Object,
                _simulationRepository.Object,
                _readingRepository.Object,
                _instrumentRepository.Object);
        }

        [Fact(DisplayName = "WithNullRequest_ReturnsBadRequest")]
        public async Task Execute_WithNullRequest_ReturnsBadRequest()
        {
            // Arrange
            AddSimulationRequest request = null;

            // Act
            var result = await _useCase.Execute(request);

            // Assert
            result.Status.Should().Be(UseCaseResponseKind.BadRequest);
        }

        [Fact(DisplayName = "WithInvalidRequest_ReturnsBadRequest")]
        public async Task Execute_WithInvalidRequest_ReturnsBadRequest()
        {
            // Arrange
            var request = new AddSimulationRequest();

            // Act
            var result = await _useCase.Execute(request);

            // Assert
            result.Status.Should().Be(UseCaseResponseKind.BadRequest);
        }

        [Fact(DisplayName = "WithValidRequest_ReturnsOk")]
        public async Task Execute_WithValidRequest_ReturnsOk()
        {
            // Arrange
            var request = AddSimulationRequestBuilder.CreateValid();

            _sectionRepository
                .Setup(r => r.GetAsync(It.IsAny<IEnumerable<Guid>>()))
                .ReturnsAsync(
                    new List<Domain.Entities.Section>() 
                    { 
                        SectionBuilder.CreateDefault().WithId(request.Sections.First().SectionId) 
                    });

            _readingRepository.Setup(x => x.ExistsBeachLengthAsync(It.IsAny<Guid>()))
                .ReturnsAsync(true);

            _readingRepository.Setup(x => x.ExistsAsync(It.IsAny<Guid>()))
                .ReturnsAsync(true);

            // Act
            var result = await _useCase.Execute(request);

            // Assert
            result.Status.Should().Be(UseCaseResponseKind.OK);
        }

        [Fact(DisplayName = "WithSpecificStatisticalMeasure_ReturnsOk")]
        public async Task Execute_WithSpecificStatisticalMeasure_ReturnsOk()
        {
            // Arrange
            var request = AddSimulationRequestBuilder.CreateValidWithSpecificWaterTable();

            var sectionDb = SectionBuilder.CreateDefault().WithId(request.Sections.First().SectionId);

            foreach (var section in request.Sections)
            {
                foreach (var instrument in section.Instruments)
                {
                    var wli = new Domain.Entities.Instrument
                    {
                        Id = instrument.InstrumentId,
                        DryType = DryType.Base,
                        BaseQuota = instrument.Quota,
                        TopQuota = instrument.Quota + 1,
                        Online = true
                    };

                    wli.SetType(InstrumentType.WaterLevelIndicator);

                    sectionDb.AddInstrument(wli);

                    var downstreamLinimetricRuler = new Domain.Entities.Instrument
                    {
                        Id = Guid.NewGuid(),
                        Online = true,
                        LinimetricRulerPosition = LinimetricRulerPosition.Downstream,
                    };

                    downstreamLinimetricRuler.SetType(InstrumentType.LinimetricRuler);

                    sectionDb.AddInstrument(downstreamLinimetricRuler);

                    var upstreamLinimetricRuler = new Domain.Entities.Instrument
                    {
                        Id = Guid.NewGuid(),
                        Online = true,
                        LinimetricRulerPosition = LinimetricRulerPosition.Upstream,
                    };

                    upstreamLinimetricRuler.SetType(InstrumentType.LinimetricRuler);

                    sectionDb.AddInstrument(upstreamLinimetricRuler);
                }
            }

            _sectionRepository
                .Setup(r => r.GetAsync(It.IsAny<IEnumerable<Guid>>()))
                .ReturnsAsync(
                    new List<Domain.Entities.Section>()
                    {
                        sectionDb
                    });

            _instrumentRepository.Setup(x => x.GetByTypeAndStructureAsync(It.IsAny<InstrumentType>(), It.IsAny<Guid>()))
                .ReturnsAsync(new List<Domain.Entities.Instrument>()
                {
                    InstrumentBuilder.CreateLinimetricRuler(),
                    InstrumentBuilder.CreateLinimetricRuler().WithLinimetricRulerPosition(LinimetricRulerPosition.Upstream),
                });

            _readingRepository.Setup(x => x.ExistsBeachLengthAsync(It.IsAny<Guid>()))
                .ReturnsAsync(true);

            _readingRepository.Setup(x => x.ExistsAsync(It.IsAny<Guid>()))
                .ReturnsAsync(true);

            // Act
            var result = await _useCase.Execute(request);

            // Assert
            result.Status.Should().Be(UseCaseResponseKind.OK);
        }

        [Fact(DisplayName = "WithException_ReturnsInternalServerError")]
        public async Task Execute_WithException_ReturnsInternalServerError()
        {
            // Arrange
            var request = AddSimulationRequestBuilder.CreateValid();

            _sectionRepository
                .Setup(r => r.GetAsync(It.IsAny<IEnumerable<Guid>>()))
                .Throws(new Exception());

            // Act
            var result = await _useCase.Execute(request);

            // Assert
            result.Status.Should().Be(UseCaseResponseKind.InternalServerError);
        }
    }
}
