using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Application.Core;
using Application.Services.BlobStorage;
using Application.Simulation.GetById;
using Database.Repositories.Simulation;
using Domain.Enums;
using Model.Simulation.GetById.Request;
using Model.Simulation.GetById.Response;

namespace Application.Tests.Simulation;

[Trait("GetSimulationByIdUseCase", "Execute")]
public class GetByIdTests
{
    private readonly Mock<ISimulationRepository> _simulationRepository;
    private readonly Mock<IBlobStorageService> _blobStorageService;
    private readonly IGetSimulationByIdUseCase _useCase;

    public GetByIdTests()
    {
        _simulationRepository = new Mock<ISimulationRepository>();
        _blobStorageService = new Mock<IBlobStorageService>();

        _useCase = new GetSimulationByIdUseCase(
            _simulationRepository.Object,
            _blobStorageService.Object);
    }

    [Fact(DisplayName = "When request is null, then returns BadRequest")]
    public async Task WhenRequestIsNull_ReturnsBadRequest()
    {
        var result = await _useCase.Execute(null);

        result.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "When request is invalid, then returns BadRequest")]
    public async Task WhenRequestIsInvalid_ReturnsBadRequest()
    {
        var request = new GetSimulationByIdRequest { Id = Guid.Empty };

        var result = await _useCase.Execute(request);

        result.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "When simulation is not found, then returns NoContent")]
    public async Task WhenSimulationNotFound_ReturnsNoContent()
    {
        var request = new GetSimulationByIdRequest
        {
            Id = Guid.NewGuid(), RequestedBy = Guid.NewGuid()
        };

        _simulationRepository
            .Setup(x => x.GetByIdAsync(It.IsAny<GetSimulationByIdRequest>()))
            .ReturnsAsync((GetSimulationByIdResponse)null);

        var result = await _useCase.Execute(request);

        result.Status.Should().Be(UseCaseResponseKind.NoContent);
    }

    [Fact(DisplayName = "When simulation exists, then returns Ok")]
    public async Task WhenSimulationExists_ReturnsOk()
    {
        var request = new GetSimulationByIdRequest
        {
            Id = Guid.NewGuid(), RequestedBy = Guid.NewGuid()
        };

        var simulationResponse = new GetSimulationByIdResponse
        {
            Id = request.Id,
            Name = "Test Simulation",
            Status = SimulationStatus.Completed,
            ShouldKeep = true,
            CreatedBy = new Model._Shared.User.User
            {
                Id = Guid.NewGuid(),
                FirstName = "Test",
                Surname = "User",
                Username = "testuser"
            },
            Sections = new List<GetSimulationByIdSection>(),
            Events = new List<GetSimulationByIdEvent>()
        };

        _simulationRepository
            .Setup(x => x.GetByIdAsync(It.IsAny<GetSimulationByIdRequest>()))
            .ReturnsAsync(simulationResponse);

        var result = await _useCase.Execute(request);

        result.Status.Should().Be(UseCaseResponseKind.OK);
        result.Result.Should().NotBeNull();
        result.Result.Id.Should().Be(request.Id);
        result.Result.Name.Should().Be("Test Simulation");
    }

    [Fact(
        DisplayName =
            "When repository throws exception, then returns InternalServerError")]
    public async Task WhenRepositoryThrowsException_ReturnsInternalServerError()
    {
        var request = new GetSimulationByIdRequest
        {
            Id = Guid.NewGuid(), RequestedBy = Guid.NewGuid()
        };

        _simulationRepository
            .Setup(x => x.GetByIdAsync(It.IsAny<GetSimulationByIdRequest>()))
            .ThrowsAsync(new Exception("Database error"));

        var result = await _useCase.Execute(request);

        result.Status.Should().Be(UseCaseResponseKind.InternalServerError);
    }
}
